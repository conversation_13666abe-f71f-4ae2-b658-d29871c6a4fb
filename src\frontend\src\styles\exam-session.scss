.exam-session-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f9fafb;

    .timer-warning {
        animation: pulse 2s infinite;
    }

    .question-navigation {
        .question-number {
            transition: all $transition-normal;

            &.current {
                transform: scale(1.1);
            }

            &.answered {
                background-color: $success-light;
                color: $success-color;
            }
        }
    }

    .option-item {
        transition: all $transition-fast;

        &:hover {
            background-color: #f9fafb;
            border-color: $primary-color;
        }

        &.selected {
            background-color: $primary-light;
            border-color: $primary-color;
        }
    }
}

@keyframes pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}
