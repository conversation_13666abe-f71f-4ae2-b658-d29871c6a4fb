.auth-container {
  background-image: linear-gradient(to right bottom, #f9fafb, #f3f4f6);
  
  .auth-form {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    
    h1 {
      color: #9333ea;
      font-weight: 800;
      letter-spacing: -0.025em;
    }
    
    input:focus {
      border-color: #9333ea;
      box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.2);
    }
    
    button[type="submit"] {
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
      }
      
      &:disabled {
        cursor: not-allowed;
      }
    }
  }
}
