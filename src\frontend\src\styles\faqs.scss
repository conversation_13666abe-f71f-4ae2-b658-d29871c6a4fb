.faqs-page {
  .hero-section {
    background: linear-gradient(to right, #9333ea, #7e22ce);
  }
  
  .faq-item {
    transition: all $transition-normal;
    
    &:hover {
      box-shadow: $shadow-md;
    }
    
    &.active {
      h3 {
        color: $primary-color;
      }
    }
    
    .faq-answer {
      animation: fadeIn 0.3s ease-in-out;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
