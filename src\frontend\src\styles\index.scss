@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden; // Prevent horizontal scroll
}

* {
  box-sizing: border-box;
}

// Custom variables
$primary-color: #ea339b; // purple-600
$primary-hover: #c5187a; // purple-700
$secondary-color: #f3f4f6; // gray-100
$text-color: #111827; // gray-900
$text-light: #6b7280; // gray-500
$border-color: #e5e7eb; // gray-200
$success-color: #10b981; // green-500
$error-color: #f51d1d; // red-500

// Import component styles
@import "auth";
@import "sidebar";
@import "dashboard";
@import "exam-form";
@import "view-exam";
@import "profile";
@import "loading-spinner";
@import "not-found";
@import "footer";
@import "public-header";
@import "home";
@import "about";
@import "faqs";
@import "take-exam";
@import "my-exams";
@import "exam-session";
@import "exam-results";
