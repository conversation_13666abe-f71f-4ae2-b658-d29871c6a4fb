.dashboard {
  .empty-state {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 2rem;
  }
  
  .exam-list {
    .exam-item {
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #f9fafb;
      }
      
      .subject-tag {
        background-color: rgba(147, 51, 234, 0.1);
      }
      
      .action-buttons {
        button, a {
          transition: all 0.2s ease;
          
          &:hover {
            transform: scale(1.05);
          }
        }
        
        .view-btn {
          background-color: #9333ea;
          &:hover {
            background-color: #7e22ce;
          }
        }
        
        .edit-btn {
          background-color: #10b981;
          &:hover {
            background-color: #059669;
          }
        }
        
        .delete-btn {
          background-color: #ef4444;
          &:hover {
            background-color: #dc2626;
          }
        }
      }
    }
  }
}
