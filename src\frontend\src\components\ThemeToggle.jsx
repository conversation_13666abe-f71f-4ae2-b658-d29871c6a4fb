import React, { useState } from 'react';
import { <PERSON>, <PERSON>, Palette, <PERSON> } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle = ({ showColorPicker = false }) => {
  const { theme, colorScheme, toggleTheme, changeColorScheme, isDark } = useTheme();
  const [showColorOptions, setShowColorOptions] = useState(false);

  const colorSchemes = [
    { 
      name: 'blue', 
      label: 'Professional Blue', 
      colors: { primary: '#2563eb', secondary: '#dbeafe' },
      description: 'Trustworthy and professional'
    },
    { 
      name: 'green', 
      label: 'Success Green', 
      colors: { primary: '#059669', secondary: '#d1fae5' },
      description: 'Growth and success focused'
    },
    { 
      name: 'orange', 
      label: 'Energetic Orange', 
      colors: { primary: '#ea580c', secondary: '#fed7aa' },
      description: 'Vibrant and engaging'
    },
    { 
      name: 'slate', 
      label: 'Modern Slate', 
      colors: { primary: '#475569', secondary: '#e2e8f0' },
      description: 'Sophisticated and modern'
    },
    { 
      name: 'purple', 
      label: 'Creative Purple', 
      colors: { primary: '#9333ea', secondary: '#f3e8ff' },
      description: 'Creative and innovative'
    },
  ];

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        {/* Theme Toggle Button */}
        <button
          onClick={toggleTheme}
          className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          title={`Switch to ${isDark ? 'light' : 'dark'} mode`}
        >
          {isDark ? (
            <Sun className="h-5 w-5 text-yellow-500" />
          ) : (
            <Moon className="h-5 w-5 text-gray-600" />
          )}
        </button>

        {/* Color Scheme Toggle Button */}
        {showColorPicker && (
          <button
            onClick={() => setShowColorOptions(!showColorOptions)}
            className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            title="Change color scheme"
          >
            <Palette className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          </button>
        )}
      </div>

      {/* Color Scheme Dropdown */}
      {showColorOptions && (
        <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Choose Color Scheme
            </h3>
            <div className="space-y-2">
              {colorSchemes.map((scheme) => (
                <button
                  key={scheme.name}
                  onClick={() => {
                    changeColorScheme(scheme.name);
                    setShowColorOptions(false);
                  }}
                  className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${
                    colorScheme === scheme.name
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex space-x-1">
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: scheme.colors.primary }}
                      />
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: scheme.colors.secondary }}
                      />
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {scheme.label}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {scheme.description}
                      </div>
                    </div>
                  </div>
                  {colorScheme === scheme.name && (
                    <Check className="h-4 w-4 text-blue-500" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {showColorOptions && (
        <div 
          className="fixed inset-0 z-40"
          onClick={() => setShowColorOptions(false)}
        />
      )}
    </div>
  );
};

export default ThemeToggle;
