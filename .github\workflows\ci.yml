name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest

    env:
      # NODE_ENV: staging
      DATABASE_URL: ${{ secrets.DATABASE_URL }}
      JWT_SECRET: ${{ secrets.JWT_SECRET }}
      REFRESH_TOKEN: ${{ secrets.REFRESH_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install dependencies
        working-directory: src/backend
        run: npm install

      - name: Lint code
        working-directory: src/backend
        run: npm run lint

      - name: Generate Prisma client
        working-directory: src/backend
        run: npx prisma generate

      - name: Run tests
        working-directory: src/backend
        run: npm run test

      - name: format code
        working-directory: src/backend
        run: npm run prettier

      - name: Check code formatting
        working-directory: src/backend
        run: npm run prettier:check
