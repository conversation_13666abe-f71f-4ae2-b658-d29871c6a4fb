name: CI

on:
  push:
    branches: [main, develop, feature/*]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install dependencies
        working-directory: src/backend
        run: npm install

      - name: Lint code
        working-directory: src/backend
        run: npm run lint

      - name: Generate Prisma client
        working-directory: src/backend
        run: npx prisma generate

      - name: Run tests
        working-directory: src/backend
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: npm run test

      - name: format code
        working-directory: src/backend
        run: npm run prettier

      - name: Check code formatting
        working-directory: src/backend
        run: npm run prettier:check
