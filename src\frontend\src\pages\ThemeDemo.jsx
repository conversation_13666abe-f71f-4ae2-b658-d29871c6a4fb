import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import ThemeToggle from '../components/ThemeToggle';
import { BookOpen, CheckCircle, Clock, Award, Sun, Moon } from 'lucide-react';

const ThemeDemo = () => {
  const { theme, colorScheme, changeColorScheme } = useTheme();

  const colorSchemes = [
    { name: 'blue', label: 'Professional Blue', primary: '#2563eb' },
    { name: 'green', label: 'Success Green', primary: '#059669' },
    { name: 'orange', label: 'Energetic Orange', primary: '#ea580c' },
    { name: 'slate', label: 'Modern Slate', primary: '#475569' },
    { name: 'purple', label: 'Creative Purple', primary: '#9333ea' },
  ];

  return (
    <div className="min-h-screen bg-primary">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-primary">Theme Demo</h1>
          <ThemeToggle showColorPicker={true} />
        </div>

        {/* Current Theme Info */}
        <div className="bg-secondary rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-primary mb-4">Current Theme</h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {theme === 'dark' ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
              <span className="text-secondary capitalize">{theme} Mode</span>
            </div>
            <div className="flex items-center space-x-2">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: colorSchemes.find(c => c.name === colorScheme)?.primary }}
              />
              <span className="text-secondary">
                {colorSchemes.find(c => c.name === colorScheme)?.label}
              </span>
            </div>
          </div>
        </div>

        {/* Color Scheme Selector */}
        <div className="bg-secondary rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-primary mb-4">Color Schemes</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {colorSchemes.map((scheme) => (
              <button
                key={scheme.name}
                onClick={() => changeColorScheme(scheme.name)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  colorScheme === scheme.name
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-primary hover:border-secondary'
                }`}
              >
                <div 
                  className="w-full h-12 rounded-md mb-2"
                  style={{ backgroundColor: scheme.primary }}
                />
                <div className="text-sm font-medium text-primary">{scheme.label}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Component Examples */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Buttons */}
          <div className="bg-secondary rounded-lg p-6">
            <h3 className="text-lg font-semibold text-primary mb-4">Buttons</h3>
            <div className="space-y-4">
              <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Primary Button
              </button>
              <button className="w-full px-4 py-2 border border-primary text-secondary rounded-md hover:bg-tertiary transition-colors">
                Secondary Button
              </button>
              <button className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                Success Button
              </button>
              <button className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                Danger Button
              </button>
            </div>
          </div>

          {/* Cards */}
          <div className="bg-secondary rounded-lg p-6">
            <h3 className="text-lg font-semibold text-primary mb-4">Cards</h3>
            <div className="space-y-4">
              <div className="bg-primary border border-primary rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                  <h4 className="font-medium text-primary">Feature Card</h4>
                </div>
                <p className="text-secondary text-sm">
                  This is an example of a feature card with proper theming.
                </p>
              </div>
              
              <div className="bg-tertiary border border-secondary rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <h4 className="font-medium text-primary">Success Card</h4>
                </div>
                <p className="text-secondary text-sm">
                  This card shows successful operations or completed tasks.
                </p>
              </div>
            </div>
          </div>

          {/* Form Elements */}
          <div className="bg-secondary rounded-lg p-6">
            <h3 className="text-lg font-semibold text-primary mb-4">Form Elements</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Text Input
                </label>
                <input
                  type="text"
                  placeholder="Enter text here..."
                  className="w-full px-3 py-2 border border-primary rounded-md bg-primary text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Select Dropdown
                </label>
                <select className="w-full px-3 py-2 border border-primary rounded-md bg-primary text-primary focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Textarea
                </label>
                <textarea
                  rows={3}
                  placeholder="Enter your message..."
                  className="w-full px-3 py-2 border border-primary rounded-md bg-primary text-primary focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                />
              </div>
            </div>
          </div>

          {/* Feature Icons */}
          <div className="bg-secondary rounded-lg p-6">
            <h3 className="text-lg font-semibold text-primary mb-4">Feature Icons</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 p-3 bg-primary rounded-lg">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-full">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                </div>
                <span className="text-primary text-sm">Easy to Use</span>
              </div>
              
              <div className="flex items-center space-x-3 p-3 bg-primary rounded-lg">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-full">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <span className="text-primary text-sm">Secure</span>
              </div>
              
              <div className="flex items-center space-x-3 p-3 bg-primary rounded-lg">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-full">
                  <Clock className="h-5 w-5 text-yellow-600" />
                </div>
                <span className="text-primary text-sm">Time Management</span>
              </div>
              
              <div className="flex items-center space-x-3 p-3 bg-primary rounded-lg">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-full">
                  <Award className="h-5 w-5 text-purple-600" />
                </div>
                <span className="text-primary text-sm">Instant Results</span>
              </div>
            </div>
          </div>
        </div>

        {/* CSS Variables Display */}
        <div className="bg-secondary rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-primary mb-4">CSS Variables</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--bg-primary)' }} />
                <span className="text-secondary">--bg-primary</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--bg-secondary)' }} />
                <span className="text-secondary">--bg-secondary</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--bg-tertiary)' }} />
                <span className="text-secondary">--bg-tertiary</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--text-primary)' }} />
                <span className="text-secondary">--text-primary</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--text-secondary)' }} />
                <span className="text-secondary">--text-secondary</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--text-tertiary)' }} />
                <span className="text-secondary">--text-tertiary</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--primary-color)' }} />
                <span className="text-secondary">--primary-color</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--primary-hover)' }} />
                <span className="text-secondary">--primary-hover</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--primary-light)' }} />
                <span className="text-secondary">--primary-light</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeDemo;
