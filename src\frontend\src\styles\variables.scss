// Default theme colors (Blue theme)
$primary-color: #2563eb; // blue-600
$primary-hover: #1d4ed8; // blue-700
$primary-light: #dbeafe; // blue-100
$primary-dark: #1e40af; // blue-800

// Light Mode Colors
$light-bg: #ffffff;
$light-bg-secondary: #f9fafb; // gray-50
$light-bg-tertiary: #f3f4f6; // gray-100
$light-text: #111827; // gray-900
$light-text-secondary: #6b7280; // gray-500
$light-text-tertiary: #9ca3af; // gray-400
$light-border: #e5e7eb; // gray-200
$light-border-secondary: #d1d5db; // gray-300

// Dark Mode Colors
$dark-bg: #0f172a; // slate-900
$dark-bg-secondary: #1e293b; // slate-800
$dark-bg-tertiary: #334155; // slate-700
$dark-text: #f8fafc; // slate-50
$dark-text-secondary: #cbd5e1; // slate-300
$dark-text-tertiary: #94a3b8; // slate-400
$dark-border: #475569; // slate-600
$dark-border-secondary: #64748b; // slate-500

// CSS Custom Properties for Theme Switching
:root {
    // Light mode (default)
    --bg-primary: #{$light-bg};
    --bg-secondary: #{$light-bg-secondary};
    --bg-tertiary: #{$light-bg-tertiary};
    --text-primary: #{$light-text};
    --text-secondary: #{$light-text-secondary};
    --text-tertiary: #{$light-text-tertiary};
    --border-primary: #{$light-border};
    --border-secondary: #{$light-border-secondary};
    --primary-color: #{$primary-color};
    --primary-hover: #{$primary-hover};
    --primary-light: #{$primary-light};
    --primary-dark: #{$primary-dark};
}

// Dark mode
[data-theme="dark"] {
    --bg-primary: #{$dark-bg};
    --bg-secondary: #{$dark-bg-secondary};
    --bg-tertiary: #{$dark-bg-tertiary};
    --text-primary: #{$dark-text};
    --text-secondary: #{$dark-text-secondary};
    --text-tertiary: #{$dark-text-tertiary};
    --border-primary: #{$dark-border};
    --border-secondary: #{$dark-border-secondary};
}

// Color scheme overrides (these will be set by JavaScript)
[data-color-scheme="blue"] {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    --primary-dark: #1e40af;
}

[data-color-scheme="green"] {
    --primary-color: #059669;
    --primary-hover: #047857;
    --primary-light: #d1fae5;
    --primary-dark: #065f46;
}

[data-color-scheme="orange"] {
    --primary-color: #ea580c;
    --primary-hover: #c2410c;
    --primary-light: #fed7aa;
    --primary-dark: #9a3412;
}

[data-color-scheme="slate"] {
    --primary-color: #475569;
    --primary-hover: #334155;
    --primary-light: #e2e8f0;
    --primary-dark: #1e293b;
}

[data-color-scheme="purple"] {
    --primary-color: #9333ea;
    --primary-hover: #7e22ce;
    --primary-light: #f3e8ff;
    --primary-dark: #6b21a8;
}

// Legacy variables for backward compatibility
$secondary-color: var(--bg-tertiary);
$secondary-dark: var(--text-tertiary);
$text-color: var(--text-primary);
$text-light: var(--text-secondary);
$text-lighter: var(--text-tertiary);
$border-color: var(--border-primary);
$border-dark: var(--border-secondary);

// Status colors
$success-color: #10b981; // green-500
$success-light: #d1fae5; // green-100
$error-color: #ef4444; // red-500
$error-light: #fee2e2; // red-100
$warning-color: #f59e0b; // amber-500
$warning-light: #fef3c7; // amber-100

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);

// Transitions
$transition-fast: 0.15s ease;
$transition-normal: 0.2s ease;
$transition-slow: 0.3s ease;

// Breakpoints (matching Tailwind defaults)
$screen-sm: 640px;
$screen-md: 768px;
$screen-lg: 1024px;
$screen-xl: 1280px;
$screen-2xl: 1536px;
